/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', 'Microsoft YaHei', sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
    padding: 30px 0;
    border-radius: 10px;
    margin-bottom: 30px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

header h1 {
    font-size: 2.5em;
    font-weight: 300;
}

/* 搜索区域样式 */
.search-section {
    background: white;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 30px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-box {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.search-box input,
.search-box select {
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 6px;
    font-size: 16px;
    transition: border-color 0.3s;
}

.search-box input:focus,
.search-box select:focus {
    outline: none;
    border-color: #667eea;
}

#searchInput {
    flex: 2;
    min-width: 200px;
}

#genderFilter {
    min-width: 120px;
}

#classFilter {
    flex: 1;
    min-width: 150px;
}

/* 表单区域样式 */
.form-section {
    background: white;
    padding: 30px;
    border-radius: 10px;
    margin-bottom: 30px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.form-section h2 {
    color: #333;
    margin-bottom: 20px;
    font-size: 1.5em;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #555;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 6px;
    font-size: 16px;
    transition: border-color 0.3s;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
}

.form-buttons {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

/* 按钮样式 */
button {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s;
    font-weight: bold;
}

button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

#submitBtn, #searchBtn, #confirmDeleteBtn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

#cancelBtn, #clearSearchBtn, #cancelDeleteBtn {
    background: #6c757d;
    color: white;
}

.edit-btn {
    background: #28a745;
    color: white;
    padding: 8px 16px;
    font-size: 14px;
    margin-right: 5px;
}

.delete-btn {
    background: #dc3545;
    color: white;
    padding: 8px 16px;
    font-size: 14px;
}

/* 表格区域样式 */
.table-section {
    background: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.table-section h2 {
    color: #333;
    margin-bottom: 20px;
    font-size: 1.5em;
}

.table-container {
    overflow-x: auto;
    margin-bottom: 20px;
}

table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

th, td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-weight: bold;
    position: sticky;
    top: 0;
}

tr:hover {
    background-color: #f8f9fa;
}

.table-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #666;
    font-weight: bold;
    flex-wrap: wrap;
    gap: 10px;
}

.data-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.data-actions button {
    padding: 8px 16px;
    font-size: 14px;
}

#exportBtn {
    background: #17a2b8;
    color: white;
}

#importBtn {
    background: #ffc107;
    color: #212529;
}

#addSampleBtn {
    background: #6f42c1;
    color: white;
}

#clearAllBtn {
    background: #dc3545;
    color: white;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: white;
    margin: 15% auto;
    padding: 30px;
    border-radius: 10px;
    width: 400px;
    max-width: 90%;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.modal-content h3 {
    margin-bottom: 15px;
    color: #333;
}

.modal-content p {
    margin-bottom: 25px;
    color: #666;
}

.modal-buttons {
    display: flex;
    gap: 10px;
    justify-content: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .search-box {
        flex-direction: column;
    }
    
    .form-buttons {
        flex-direction: column;
    }
    
    .modal-buttons {
        flex-direction: column;
    }
    
    table {
        font-size: 14px;
    }
    
    th, td {
        padding: 8px;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.form-section, .table-section, .search-section {
    animation: fadeIn 0.5s ease-out;
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 40px;
    color: #666;
}

.empty-state h3 {
    margin-bottom: 10px;
    color: #999;
}
