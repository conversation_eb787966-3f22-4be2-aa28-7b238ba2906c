/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* 主色调 */
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --primary-light: #3b82f6;

    /* 辅助色 */
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #06b6d4;

    /* 中性色 */
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;

    /* 布局 */
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 80px;
    --topbar-height: 70px;

    /* 阴影 */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);

    /* 圆角 */
    --radius-sm: 0.375rem;
    --radius: 0.5rem;
    --radius-md: 0.75rem;
    --radius-lg: 1rem;

    /* 过渡 */
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Microsoft YaHei', sans-serif;
    background-color: var(--gray-50);
    color: var(--gray-900);
    line-height: 1.6;
    overflow-x: hidden;
}

/* 侧边栏样式 */
.sidebar {
    position: fixed;
    left: 0;
    top: 0;
    width: var(--sidebar-width);
    height: 100vh;
    background: linear-gradient(180deg, var(--gray-900) 0%, var(--gray-800) 100%);
    color: white;
    z-index: 1000;
    transition: var(--transition);
    box-shadow: var(--shadow-lg);
}

.sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
}

.sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem 1.25rem;
    border-bottom: 1px solid var(--gray-700);
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.25rem;
    font-weight: 700;
    color: white;
}

.logo i {
    font-size: 1.5rem;
    color: var(--primary-light);
}

.sidebar.collapsed .logo span {
    display: none;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: var(--gray-400);
    font-size: 1.25rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--radius);
    transition: var(--transition);
}

.sidebar-toggle:hover {
    color: white;
    background: var(--gray-700);
}

.sidebar-nav {
    padding: 1rem 0;
}

.sidebar-nav ul {
    list-style: none;
}

.nav-item {
    margin: 0.25rem 0;
}

.nav-item a {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.875rem 1.25rem;
    color: var(--gray-300);
    text-decoration: none;
    transition: var(--transition);
    border-radius: 0;
    position: relative;
}

.nav-item a:hover {
    background: var(--gray-700);
    color: white;
}

.nav-item.active a {
    background: var(--primary-color);
    color: white;
}

.nav-item.active a::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--primary-light);
}

.nav-item i {
    font-size: 1.125rem;
    width: 1.25rem;
    text-align: center;
}

.sidebar.collapsed .nav-item span {
    display: none;
}

/* 主内容包装器 */
.main-wrapper {
    margin-left: var(--sidebar-width);
    min-height: 100vh;
    transition: var(--transition);
}

.sidebar.collapsed + .main-wrapper {
    margin-left: var(--sidebar-collapsed-width);
}

/* 顶部导航栏 */
.topbar {
    position: sticky;
    top: 0;
    height: var(--topbar-height);
    background: white;
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 2rem;
    z-index: 100;
    box-shadow: var(--shadow-sm);
}

.topbar-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.menu-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 1.25rem;
    color: var(--gray-600);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--radius);
    transition: var(--transition);
}

.menu-toggle:hover {
    background: var(--gray-100);
}

.breadcrumb {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--gray-900);
}

.topbar-right {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.search-box-header {
    position: relative;
    display: flex;
    align-items: center;
}

.search-box-header i {
    position: absolute;
    left: 1rem;
    color: var(--gray-400);
    z-index: 1;
}

.search-box-header input {
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-lg);
    background: var(--gray-50);
    font-size: 0.875rem;
    width: 300px;
    transition: var(--transition);
}

.search-box-header input:focus {
    outline: none;
    border-color: var(--primary-color);
    background: white;
    box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

.user-menu {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.notifications {
    position: relative;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--radius);
    transition: var(--transition);
}

.notifications:hover {
    background: var(--gray-100);
}

.notifications i {
    font-size: 1.25rem;
    color: var(--gray-600);
}

.notifications .badge {
    position: absolute;
    top: 0.25rem;
    right: 0.25rem;
    background: var(--danger-color);
    color: white;
    font-size: 0.75rem;
    padding: 0.125rem 0.375rem;
    border-radius: 9999px;
    min-width: 1.25rem;
    text-align: center;
}

.user-profile {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--radius);
    transition: var(--transition);
}

.user-profile:hover {
    background: var(--gray-100);
}

.avatar {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    object-fit: cover;
}

.username {
    font-weight: 500;
    color: var(--gray-700);
}

.user-profile i {
    color: var(--gray-400);
    font-size: 0.875rem;
}

/* 主内容区域 */
.main-content {
    padding: 2rem;
    min-height: calc(100vh - var(--topbar-height));
}

/* 页面内容 */
.page-content {
    display: none;
}

.page-content.active {
    display: block;
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(1rem);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 页面头部 */
.page-header {
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 1rem;
}

.page-header h1 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--gray-900);
    margin: 0;
}

.page-header p {
    color: var(--gray-600);
    margin: 0.5rem 0 0 0;
}

.page-actions {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

/* 统计卡片网格 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow);
    border: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: var(--transition);
}

.stat-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.stat-icon {
    width: 3rem;
    height: 3rem;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-card:nth-child(1) .stat-icon {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
}

.stat-card:nth-child(2) .stat-icon {
    background: linear-gradient(135deg, var(--info-color), #0891b2);
}

.stat-card:nth-child(3) .stat-icon {
    background: linear-gradient(135deg, var(--success-color), #059669);
}

.stat-card:nth-child(4) .stat-icon {
    background: linear-gradient(135deg, var(--warning-color), #d97706);
}

.stat-content h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--gray-900);
    margin: 0;
}

.stat-content p {
    color: var(--gray-600);
    margin: 0.25rem 0 0 0;
    font-size: 0.875rem;
}

/* 快速操作 */
.quick-actions {
    margin-bottom: 2rem;
}

.quick-actions h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 1rem;
}

.action-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.action-card {
    background: white;
    padding: 1.5rem;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow);
    border: 1px solid var(--gray-200);
    cursor: pointer;
    transition: var(--transition);
    text-align: center;
}

.action-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
    border-color: var(--primary-color);
}

.action-card i {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.action-card h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 0.5rem;
}

.action-card p {
    color: var(--gray-600);
    font-size: 0.875rem;
    margin: 0;
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    border: none;
    border-radius: var(--radius);
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    white-space: nowrap;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background: var(--gray-600);
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background: var(--gray-700);
}

.btn-outline {
    background: transparent;
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
}

.btn-outline:hover:not(:disabled) {
    background: var(--gray-50);
    border-color: var(--gray-400);
}

.btn-danger {
    background: var(--danger-color);
    color: white;
}

.btn-danger:hover:not(:disabled) {
    background: #dc2626;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.8125rem;
}

.edit-btn {
    background: var(--success-color);
    color: white;
    padding: 0.5rem 1rem;
    font-size: 0.8125rem;
    border: none;
    border-radius: var(--radius);
    cursor: pointer;
    transition: var(--transition);
}

.edit-btn:hover {
    background: #059669;
    transform: translateY(-1px);
}

.delete-btn {
    background: var(--danger-color);
    color: white;
    padding: 0.5rem 1rem;
    font-size: 0.8125rem;
    border: none;
    border-radius: var(--radius);
    cursor: pointer;
    transition: var(--transition);
}

.delete-btn:hover {
    background: #dc2626;
    transform: translateY(-1px);
}

/* 筛选区域 */
.filters-section {
    background: white;
    padding: 1.5rem;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow);
    border: 1px solid var(--gray-200);
    margin-bottom: 1.5rem;
}

.search-filters {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
}

.search-input-group {
    position: relative;
    flex: 1;
    min-width: 250px;
}

.search-input-group i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-400);
    z-index: 1;
}

.search-input-group input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 1px solid var(--gray-300);
    border-radius: var(--radius);
    font-size: 0.875rem;
    transition: var(--transition);
}

.search-input-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

.filter-select,
.filter-input {
    padding: 0.75rem 1rem;
    border: 1px solid var(--gray-300);
    border-radius: var(--radius);
    font-size: 0.875rem;
    transition: var(--transition);
    min-width: 120px;
}

.filter-select:focus,
.filter-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

/* 数据表格容器 */
.data-table-container {
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow);
    border: 1px solid var(--gray-200);
    overflow: hidden;
}

.table-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem;
    border-bottom: 1px solid var(--gray-200);
    background: var(--gray-50);
}

.table-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
}

.table-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.table-wrapper {
    overflow-x: auto;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
}

.data-table th,
.data-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid var(--gray-200);
}

.data-table th {
    background: var(--gray-50);
    font-weight: 600;
    color: var(--gray-700);
    position: sticky;
    top: 0;
    z-index: 10;
}

.data-table tbody tr {
    transition: var(--transition);
}

.data-table tbody tr:hover {
    background: var(--gray-50);
}

.data-table tbody tr:last-child td {
    border-bottom: none;
}

/* 最近学生列表 */
.recent-students {
    margin-bottom: 2rem;
}

.recent-students h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 1rem;
}

.recent-list {
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow);
    border: 1px solid var(--gray-200);
    overflow: hidden;
}

.recent-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--gray-200);
    transition: var(--transition);
}

.recent-item:last-child {
    border-bottom: none;
}

.recent-item:hover {
    background: var(--gray-50);
}

.recent-info h4 {
    font-weight: 600;
    color: var(--gray-900);
    margin: 0 0 0.25rem 0;
}

.recent-info p {
    color: var(--gray-600);
    font-size: 0.875rem;
    margin: 0;
}

.recent-time {
    color: var(--gray-500);
    font-size: 0.8125rem;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    backdrop-filter: blur(4px);
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
    animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.modal-content {
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    animation: modalSlideIn 0.3s ease-out;
}

.modal-content.large {
    max-width: 800px;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-2rem) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem;
    border-bottom: 1px solid var(--gray-200);
}

.modal-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.25rem;
    color: var(--gray-400);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--radius);
    transition: var(--transition);
}

.modal-close:hover {
    background: var(--gray-100);
    color: var(--gray-600);
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    display: flex;
    gap: 0.75rem;
    justify-content: flex-end;
    padding: 1.5rem;
    border-top: 1px solid var(--gray-200);
    background: var(--gray-50);
}

/* 表单样式 */
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-group label {
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.required {
    color: var(--danger-color);
}

.form-group input,
.form-group select {
    padding: 0.75rem;
    border: 1px solid var(--gray-300);
    border-radius: var(--radius);
    font-size: 0.875rem;
    transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

/* 删除警告 */
.delete-warning {
    text-align: center;
    padding: 1rem;
}

.delete-warning i {
    font-size: 3rem;
    color: var(--warning-color);
    margin-bottom: 1rem;
}

.delete-warning p {
    color: var(--gray-700);
    margin: 0;
}

/* 加载指示器 */
.loading-indicator {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    z-index: 3000;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    gap: 1rem;
}

.loading-indicator.show {
    display: flex;
}

.spinner {
    width: 3rem;
    height: 3rem;
    border: 4px solid var(--gray-200);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 分析页面样式 */
.analytics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
}

.chart-card {
    background: white;
    padding: 1.5rem;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow);
    border: 1px solid var(--gray-200);
}

.chart-card h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 1rem;
}

.chart-container {
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--gray-50);
    border-radius: var(--radius);
    color: var(--gray-500);
    font-style: italic;
}

/* 设置页面样式 */
.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.setting-card {
    background: white;
    padding: 1.5rem;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow);
    border: 1px solid var(--gray-200);
}

.setting-card h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 1rem;
}

.setting-actions {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.system-info p {
    margin: 0.5rem 0;
    color: var(--gray-600);
    font-size: 0.875rem;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--gray-500);
}

.empty-state h3 {
    font-size: 1.25rem;
    color: var(--gray-600);
    margin-bottom: 0.5rem;
}

.empty-state p {
    color: var(--gray-500);
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .sidebar {
        transform: translateX(-100%);
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .main-wrapper {
        margin-left: 0;
    }

    .menu-toggle {
        display: block;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    .action-cards {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (max-width: 768px) {
    .main-content {
        padding: 1rem;
    }

    .page-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .topbar {
        padding: 0 1rem;
    }

    .search-box-header {
        display: none;
    }

    .search-filters {
        flex-direction: column;
        align-items: stretch;
    }

    .search-input-group {
        min-width: auto;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .action-cards {
        grid-template-columns: 1fr;
    }

    .analytics-grid {
        grid-template-columns: 1fr;
    }

    .settings-grid {
        grid-template-columns: 1fr;
    }

    .form-grid {
        grid-template-columns: 1fr;
    }

    .modal-content {
        width: 95%;
        margin: 5% auto;
    }

    .modal-footer {
        flex-direction: column-reverse;
    }

    .table-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .table-actions {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .data-table {
        font-size: 0.8125rem;
    }

    .data-table th,
    .data-table td {
        padding: 0.75rem 0.5rem;
    }
}

@media (max-width: 480px) {
    .topbar-right {
        gap: 0.5rem;
    }

    .user-profile .username {
        display: none;
    }

    .btn {
        padding: 0.625rem 1rem;
        font-size: 0.8125rem;
    }

    .page-actions {
        width: 100%;
    }

    .page-actions .btn {
        flex: 1;
        justify-content: center;
    }
}

/* 消息提示样式 */
.message {
    position: fixed;
    top: 1rem;
    right: 1rem;
    padding: 1rem 1.5rem;
    border-radius: var(--radius);
    color: white;
    font-weight: 500;
    z-index: 2500;
    max-width: 400px;
    box-shadow: var(--shadow-lg);
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.message.success {
    background: var(--success-color);
}

.message.error {
    background: var(--danger-color);
}

.message.info {
    background: var(--info-color);
}

.message.warning {
    background: var(--warning-color);
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--gray-100);
}

::-webkit-scrollbar-thumb {
    background: var(--gray-300);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gray-400);
}

/* 选择框样式 */
input[type="checkbox"] {
    width: 1rem;
    height: 1rem;
    accent-color: var(--primary-color);
}

/* 焦点样式 */
*:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* 表格徽章样式 */
.gender-badge {
    padding: 0.25rem 0.5rem;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
}

.gender-badge.male {
    background: #dbeafe;
    color: #1d4ed8;
}

.gender-badge.female {
    background: #fce7f3;
    color: #be185d;
}

.class-badge {
    padding: 0.25rem 0.5rem;
    background: var(--gray-100);
    color: var(--gray-700);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
}

/* 额外的动画效果 */
.btn:active {
    transform: translateY(0) scale(0.98);
}

.stat-card:hover .stat-icon {
    transform: scale(1.1);
}

.action-card:hover i {
    transform: scale(1.2);
    color: var(--primary-light);
}

/* 表格行悬停效果 */
.data-table tbody tr {
    position: relative;
}

.data-table tbody tr::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 0;
    background: linear-gradient(90deg, var(--primary-color), transparent);
    transition: width 0.3s ease;
}

.data-table tbody tr:hover::before {
    width: 4px;
}

/* 搜索框聚焦效果 */
.search-input-group input:focus + i {
    color: var(--primary-color);
    transform: translateY(-50%) scale(1.1);
}

/* 侧边栏收缩动画 */
.sidebar .nav-item a {
    overflow: hidden;
}

.sidebar.collapsed .nav-item a {
    justify-content: center;
}

.sidebar.collapsed .nav-item a span {
    opacity: 0;
    transform: translateX(-20px);
    transition: all 0.3s ease;
}

.sidebar:not(.collapsed) .nav-item a span {
    opacity: 1;
    transform: translateX(0);
    transition: all 0.3s ease 0.1s;
}

/* 模态框背景模糊效果 */
.modal {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
}

/* 加载动画增强 */
.spinner {
    position: relative;
}

.spinner::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 1rem;
    height: 1rem;
    margin: -0.5rem 0 0 -0.5rem;
    border: 2px solid transparent;
    border-top: 2px solid var(--primary-light);
    border-radius: 50%;
    animation: spin 0.8s linear infinite reverse;
}

/* 通知徽章动画 */
.notifications .badge {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

/* 页面切换动画增强 */
.page-content {
    opacity: 0;
    transform: translateY(2rem);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.page-content.active {
    opacity: 1;
    transform: translateY(0);
}

/* 表单验证状态 */
.form-group input:invalid {
    border-color: var(--danger-color);
    box-shadow: 0 0 0 3px rgb(239 68 68 / 0.1);
}

.form-group input:valid {
    border-color: var(--success-color);
}

/* 工具提示样式 */
[title] {
    position: relative;
}

[title]:hover::after {
    content: attr(title);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--gray-900);
    color: white;
    padding: 0.5rem;
    border-radius: var(--radius);
    font-size: 0.75rem;
    white-space: nowrap;
    z-index: 1000;
    animation: tooltipFadeIn 0.2s ease-out;
}

@keyframes tooltipFadeIn {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(0.5rem);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

/* 移动端优化 */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .main-wrapper {
        margin-left: 0;
    }

    /* 移动端模态框全屏 */
    .modal-content {
        width: 100%;
        height: 100%;
        max-width: none;
        max-height: none;
        border-radius: 0;
        margin: 0;
    }
}
