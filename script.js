// 学生管理系统 JavaScript 代码

class StudentManager {
    constructor() {
        this.students = this.loadStudents();
        this.currentEditId = null;
        this.filteredStudents = [...this.students];
        this.init();
    }

    // 初始化
    init() {
        this.bindEvents();
        this.renderTable();
        this.updateTotalCount();
    }

    // 绑定事件
    bindEvents() {
        // 表单提交事件
        document.getElementById('studentForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleFormSubmit();
        });

        // 取消按钮事件
        document.getElementById('cancelBtn').addEventListener('click', () => {
            this.resetForm();
        });

        // 搜索事件
        document.getElementById('searchBtn').addEventListener('click', () => {
            this.handleSearch();
        });

        // 清除搜索事件
        document.getElementById('clearSearchBtn').addEventListener('click', () => {
            this.clearSearch();
        });

        // 搜索输入框回车事件
        document.getElementById('searchInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.handleSearch();
            }
        });

        // 筛选器变化事件
        document.getElementById('genderFilter').addEventListener('change', () => {
            this.handleSearch();
        });

        document.getElementById('classFilter').addEventListener('input', () => {
            this.handleSearch();
        });

        document.getElementById('classFilter').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.handleSearch();
            }
        });

        // 模态框事件
        document.getElementById('confirmDeleteBtn').addEventListener('click', () => {
            this.confirmDelete();
        });

        document.getElementById('cancelDeleteBtn').addEventListener('click', () => {
            this.hideDeleteModal();
        });

        // 点击模态框外部关闭
        document.getElementById('deleteModal').addEventListener('click', (e) => {
            if (e.target.id === 'deleteModal') {
                this.hideDeleteModal();
            }
        });

        // 数据操作事件
        document.getElementById('exportBtn').addEventListener('click', () => {
            this.exportData();
        });

        document.getElementById('importBtn').addEventListener('click', () => {
            document.getElementById('importFile').click();
        });

        document.getElementById('importFile').addEventListener('change', (e) => {
            this.importData(e.target.files[0]);
        });

        document.getElementById('addSampleBtn').addEventListener('click', () => {
            this.addSampleData();
        });

        document.getElementById('clearAllBtn').addEventListener('click', () => {
            this.clearAllData();
        });
    }

    // 处理表单提交
    handleFormSubmit() {
        const formData = this.getFormData();
        
        // 验证数据
        if (!this.validateFormData(formData)) {
            return;
        }

        if (this.currentEditId) {
            // 编辑模式
            this.updateStudent(this.currentEditId, formData);
            this.showMessage('学生信息更新成功！', 'success');
        } else {
            // 添加模式
            if (this.isStudentIdExists(formData.studentId)) {
                this.showMessage('学号已存在，请使用其他学号！', 'error');
                return;
            }
            this.addStudent(formData);
            this.showMessage('学生添加成功！', 'success');
        }

        this.resetForm();
        this.renderTable();
        this.updateTotalCount();
        this.saveStudents();
    }

    // 获取表单数据
    getFormData() {
        return {
            studentId: document.getElementById('studentId').value.trim(),
            studentName: document.getElementById('studentName').value.trim(),
            studentAge: parseInt(document.getElementById('studentAge').value),
            studentGender: document.getElementById('studentGender').value,
            studentClass: document.getElementById('studentClass').value.trim(),
            studentPhone: document.getElementById('studentPhone').value.trim(),
            studentEmail: document.getElementById('studentEmail').value.trim()
        };
    }

    // 验证表单数据
    validateFormData(data) {
        if (!data.studentId || !data.studentName || !data.studentAge || !data.studentGender || !data.studentClass) {
            this.showMessage('请填写所有必填字段！', 'error');
            return false;
        }

        if (data.studentAge < 1 || data.studentAge > 100) {
            this.showMessage('年龄必须在1-100之间！', 'error');
            return false;
        }

        if (data.studentPhone && !/^1[3-9]\d{9}$/.test(data.studentPhone)) {
            this.showMessage('请输入正确的手机号码！', 'error');
            return false;
        }

        if (data.studentEmail && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.studentEmail)) {
            this.showMessage('请输入正确的邮箱地址！', 'error');
            return false;
        }

        return true;
    }

    // 检查学号是否存在
    isStudentIdExists(studentId) {
        return this.students.some(student => 
            student.studentId === studentId && student.id !== this.currentEditId
        );
    }

    // 添加学生
    addStudent(studentData) {
        const student = {
            id: Date.now().toString(),
            ...studentData,
            createTime: new Date().toLocaleString()
        };
        this.students.push(student);
        this.filteredStudents = [...this.students];
    }

    // 更新学生
    updateStudent(id, studentData) {
        const index = this.students.findIndex(student => student.id === id);
        if (index !== -1) {
            this.students[index] = {
                ...this.students[index],
                ...studentData,
                updateTime: new Date().toLocaleString()
            };
            this.filteredStudents = [...this.students];
        }
    }

    // 删除学生
    deleteStudent(id) {
        this.students = this.students.filter(student => student.id !== id);
        this.filteredStudents = [...this.students];
        this.renderTable();
        this.updateTotalCount();
        this.saveStudents();
        this.showMessage('学生删除成功！', 'success');
    }

    // 编辑学生
    editStudent(id) {
        const student = this.students.find(s => s.id === id);
        if (student) {
            this.currentEditId = id;
            this.fillForm(student);
            document.getElementById('submitBtn').textContent = '更新学生';
            document.querySelector('.form-section h2').textContent = '编辑学生信息';
            
            // 滚动到表单区域
            document.querySelector('.form-section').scrollIntoView({ 
                behavior: 'smooth' 
            });
        }
    }

    // 填充表单
    fillForm(student) {
        document.getElementById('studentId').value = student.studentId;
        document.getElementById('studentName').value = student.studentName;
        document.getElementById('studentAge').value = student.studentAge;
        document.getElementById('studentGender').value = student.studentGender;
        document.getElementById('studentClass').value = student.studentClass;
        document.getElementById('studentPhone').value = student.studentPhone || '';
        document.getElementById('studentEmail').value = student.studentEmail || '';
    }

    // 重置表单
    resetForm() {
        document.getElementById('studentForm').reset();
        this.currentEditId = null;
        document.getElementById('submitBtn').textContent = '添加学生';
        document.querySelector('.form-section h2').textContent = '添加/编辑学生信息';
    }

    // 处理搜索
    handleSearch() {
        const searchTerm = document.getElementById('searchInput').value.trim().toLowerCase();
        const genderFilter = document.getElementById('genderFilter').value;
        const classFilter = document.getElementById('classFilter').value.trim().toLowerCase();

        this.filteredStudents = this.students.filter(student => {
            // 文本搜索匹配
            const textMatch = !searchTerm ||
                student.studentName.toLowerCase().includes(searchTerm) ||
                student.studentId.toLowerCase().includes(searchTerm) ||
                student.studentClass.toLowerCase().includes(searchTerm);

            // 性别筛选匹配
            const genderMatch = !genderFilter || student.studentGender === genderFilter;

            // 班级筛选匹配
            const classMatch = !classFilter ||
                student.studentClass.toLowerCase().includes(classFilter);

            return textMatch && genderMatch && classMatch;
        });

        this.renderTable();
        this.updateTotalCount();
    }

    // 清除搜索
    clearSearch() {
        document.getElementById('searchInput').value = '';
        document.getElementById('genderFilter').value = '';
        document.getElementById('classFilter').value = '';
        this.filteredStudents = [...this.students];
        this.renderTable();
        this.updateTotalCount();
    }

    // 渲染表格
    renderTable() {
        const tbody = document.getElementById('studentTableBody');
        
        if (this.filteredStudents.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" class="empty-state">
                        <h3>暂无学生数据</h3>
                        <p>请添加学生信息</p>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = this.filteredStudents.map(student => `
            <tr>
                <td>${student.studentId}</td>
                <td>${student.studentName}</td>
                <td>${student.studentAge}</td>
                <td>${student.studentGender}</td>
                <td>${student.studentClass}</td>
                <td>${student.studentPhone || '-'}</td>
                <td>${student.studentEmail || '-'}</td>
                <td>
                    <button class="edit-btn" onclick="studentManager.editStudent('${student.id}')">
                        编辑
                    </button>
                    <button class="delete-btn" onclick="studentManager.showDeleteModal('${student.id}')">
                        删除
                    </button>
                </td>
            </tr>
        `).join('');
    }

    // 更新总数显示
    updateTotalCount() {
        const totalElement = document.getElementById('totalCount');
        const total = this.filteredStudents.length;
        const allTotal = this.students.length;
        
        if (total === allTotal) {
            totalElement.textContent = `总计: ${total} 名学生`;
        } else {
            totalElement.textContent = `显示: ${total} 名学生 (总计: ${allTotal} 名)`;
        }
    }

    // 显示删除确认模态框
    showDeleteModal(id) {
        this.deleteTargetId = id;
        document.getElementById('deleteModal').style.display = 'block';
    }

    // 隐藏删除确认模态框
    hideDeleteModal() {
        document.getElementById('deleteModal').style.display = 'none';
        this.deleteTargetId = null;
    }

    // 确认删除
    confirmDelete() {
        if (this.deleteTargetId) {
            this.deleteStudent(this.deleteTargetId);
            this.hideDeleteModal();
        }
    }

    // 显示消息
    showMessage(message, type = 'info') {
        // 创建消息元素
        const messageDiv = document.createElement('div');
        messageDiv.className = `message message-${type}`;
        messageDiv.textContent = message;
        
        // 添加样式
        messageDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 5px;
            color: white;
            font-weight: bold;
            z-index: 1001;
            animation: slideIn 0.3s ease-out;
            max-width: 300px;
            word-wrap: break-word;
        `;
        
        // 根据类型设置背景色
        switch (type) {
            case 'success':
                messageDiv.style.backgroundColor = '#28a745';
                break;
            case 'error':
                messageDiv.style.backgroundColor = '#dc3545';
                break;
            default:
                messageDiv.style.backgroundColor = '#17a2b8';
        }
        
        document.body.appendChild(messageDiv);
        
        // 3秒后自动移除
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.style.animation = 'slideOut 0.3s ease-out';
                setTimeout(() => {
                    document.body.removeChild(messageDiv);
                }, 300);
            }
        }, 3000);
    }

    // 保存到本地存储
    saveStudents() {
        localStorage.setItem('students', JSON.stringify(this.students));
    }

    // 从本地存储加载
    loadStudents() {
        const saved = localStorage.getItem('students');
        return saved ? JSON.parse(saved) : [];
    }

    // 导出数据
    exportData() {
        const dataStr = JSON.stringify(this.students, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(dataBlob);

        const link = document.createElement('a');
        link.href = url;
        link.download = `学生数据_${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        this.showMessage('数据导出成功！', 'success');
    }

    // 导入数据
    importData(file) {
        if (!file) return;

        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const importedData = JSON.parse(e.target.result);

                if (!Array.isArray(importedData)) {
                    throw new Error('数据格式不正确');
                }

                // 验证数据结构
                const isValidData = importedData.every(item =>
                    item.studentId && item.studentName && item.studentAge &&
                    item.studentGender && item.studentClass
                );

                if (!isValidData) {
                    throw new Error('数据结构不完整');
                }

                // 合并数据，避免重复学号
                const existingIds = new Set(this.students.map(s => s.studentId));
                const newStudents = importedData.filter(s => !existingIds.has(s.studentId));

                this.students.push(...newStudents);
                this.filteredStudents = [...this.students];
                this.renderTable();
                this.updateTotalCount();
                this.saveStudents();

                this.showMessage(`成功导入 ${newStudents.length} 名学生数据！`, 'success');

            } catch (error) {
                this.showMessage('导入失败：' + error.message, 'error');
            }
        };

        reader.readAsText(file);
        // 清空文件输入
        document.getElementById('importFile').value = '';
    }

    // 添加示例数据
    addSampleData() {
        const sampleStudents = [
            {
                id: Date.now().toString() + '1',
                studentId: '2024001',
                studentName: '张三',
                studentAge: 20,
                studentGender: '男',
                studentClass: '计算机科学与技术1班',
                studentPhone: '13800138001',
                studentEmail: '<EMAIL>',
                createTime: new Date().toLocaleString()
            },
            {
                id: Date.now().toString() + '2',
                studentId: '2024002',
                studentName: '李四',
                studentAge: 19,
                studentGender: '女',
                studentClass: '软件工程1班',
                studentPhone: '13800138002',
                studentEmail: '<EMAIL>',
                createTime: new Date().toLocaleString()
            },
            {
                id: Date.now().toString() + '3',
                studentId: '2024003',
                studentName: '王五',
                studentAge: 21,
                studentGender: '男',
                studentClass: '计算机科学与技术1班',
                studentPhone: '13800138003',
                studentEmail: '<EMAIL>',
                createTime: new Date().toLocaleString()
            },
            {
                id: Date.now().toString() + '4',
                studentId: '2024004',
                studentName: '赵六',
                studentAge: 20,
                studentGender: '女',
                studentClass: '数据科学与大数据技术1班',
                studentPhone: '13800138004',
                studentEmail: '<EMAIL>',
                createTime: new Date().toLocaleString()
            }
        ];

        // 检查是否已存在示例数据
        const existingIds = new Set(this.students.map(s => s.studentId));
        const newSamples = sampleStudents.filter(s => !existingIds.has(s.studentId));

        if (newSamples.length === 0) {
            this.showMessage('示例数据已存在！', 'info');
            return;
        }

        this.students.push(...newSamples);
        this.filteredStudents = [...this.students];
        this.renderTable();
        this.updateTotalCount();
        this.saveStudents();

        this.showMessage(`成功添加 ${newSamples.length} 条示例数据！`, 'success');
    }

    // 清空所有数据
    clearAllData() {
        if (this.students.length === 0) {
            this.showMessage('没有数据需要清空！', 'info');
            return;
        }

        if (confirm('确定要清空所有学生数据吗？此操作不可撤销！')) {
            this.students = [];
            this.filteredStudents = [];
            this.renderTable();
            this.updateTotalCount();
            this.saveStudents();
            this.showMessage('所有数据已清空！', 'success');
        }
    }
}

// 添加CSS动画
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);

// 初始化应用
let studentManager;
document.addEventListener('DOMContentLoaded', () => {
    studentManager = new StudentManager();
});
