// 企业级学生管理系统 JavaScript 代码

class StudentManager {
    constructor() {
        this.students = this.loadStudents();
        this.currentEditId = null;
        this.filteredStudents = [...this.students];
        this.currentPage = 'dashboard';
        this.init();
    }

    // 初始化
    init() {
        this.bindEvents();
        this.updateDashboard();
        this.renderTable();
        this.updateTotalCount();
        this.updateRecentStudents();
        this.updateSystemInfo();
    }

    // 绑定事件
    bindEvents() {
        // 导航事件
        this.bindNavigationEvents();

        // 表单事件
        this.bindFormEvents();

        // 搜索事件
        this.bindSearchEvents();

        // 模态框事件
        this.bindModalEvents();

        // 数据操作事件
        this.bindDataEvents();
    }

    // 绑定导航事件
    bindNavigationEvents() {
        // 侧边栏导航
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const page = item.dataset.page;
                this.switchPage(page);
            });
        });

        // 侧边栏切换
        document.getElementById('sidebarToggle')?.addEventListener('click', () => {
            document.getElementById('sidebar').classList.toggle('collapsed');
        });

        // 移动端菜单切换
        document.getElementById('menuToggle')?.addEventListener('click', () => {
            document.getElementById('sidebar').classList.toggle('show');
        });

        // 全局搜索
        document.getElementById('globalSearch')?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.handleGlobalSearch(e.target.value);
            }
        });
    }

    // 绑定表单事件
    bindFormEvents() {
        // 表单提交事件
        document.getElementById('studentForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleFormSubmit();
        });

        // 添加学生按钮
        document.getElementById('addStudentBtn')?.addEventListener('click', () => {
            this.showStudentModal();
        });

        // 取消按钮事件
        document.getElementById('cancelBtn').addEventListener('click', () => {
            this.hideStudentModal();
        });
    }

    // 绑定搜索事件
    bindSearchEvents() {
        // 搜索输入框事件
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('input', () => {
                this.handleSearch();
            });

            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.handleSearch();
                }
            });
        }

        // 筛选器变化事件
        document.getElementById('genderFilter')?.addEventListener('change', () => {
            this.handleSearch();
        });

        document.getElementById('classFilter')?.addEventListener('input', () => {
            this.handleSearch();
        });

        // 清除搜索事件
        document.getElementById('clearSearchBtn')?.addEventListener('click', () => {
            this.clearSearch();
        });
    }

    // 绑定模态框事件
    bindModalEvents() {
        // 学生模态框
        document.getElementById('closeModal')?.addEventListener('click', () => {
            this.hideStudentModal();
        });

        document.getElementById('studentModal')?.addEventListener('click', (e) => {
            if (e.target.id === 'studentModal') {
                this.hideStudentModal();
            }
        });

        // 删除确认模态框
        document.getElementById('confirmDeleteBtn')?.addEventListener('click', () => {
            this.confirmDelete();
        });

        document.getElementById('cancelDeleteBtn')?.addEventListener('click', () => {
            this.hideDeleteModal();
        });

        document.getElementById('closeDeleteModal')?.addEventListener('click', () => {
            this.hideDeleteModal();
        });

        document.getElementById('deleteModal')?.addEventListener('click', (e) => {
            if (e.target.id === 'deleteModal') {
                this.hideDeleteModal();
            }
        });
    }

    // 绑定数据操作事件
    bindDataEvents() {
        // 导出数据
        document.getElementById('exportBtn')?.addEventListener('click', () => {
            this.exportData();
        });

        // 导入数据
        document.getElementById('importBtn')?.addEventListener('click', () => {
            document.getElementById('importFile').click();
        });

        document.getElementById('importFile')?.addEventListener('change', (e) => {
            this.importData(e.target.files[0]);
        });

        // 添加示例数据
        document.getElementById('addSampleBtn')?.addEventListener('click', () => {
            this.addSampleData();
        });

        // 清空所有数据
        document.getElementById('clearAllBtn')?.addEventListener('click', () => {
            this.clearAllData();
        });

        // 全选功能
        document.getElementById('selectAll')?.addEventListener('change', (e) => {
            this.handleSelectAll(e.target.checked);
        });
    }

    // 页面切换
    switchPage(page) {
        // 更新导航状态
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-page="${page}"]`)?.classList.add('active');

        // 更新页面内容
        document.querySelectorAll('.page-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(`${page}-page`)?.classList.add('active');

        // 更新面包屑
        const titles = {
            dashboard: '仪表板',
            students: '学生管理',
            analytics: '数据分析',
            settings: '系统设置'
        };
        document.getElementById('currentPageTitle').textContent = titles[page] || page;

        this.currentPage = page;

        // 根据页面更新数据
        if (page === 'dashboard') {
            this.updateDashboard();
            this.updateRecentStudents();
        } else if (page === 'students') {
            this.renderTable();
        } else if (page === 'analytics') {
            this.updateAnalytics();
        }
    }

    // 全局搜索
    handleGlobalSearch(query) {
        if (!query.trim()) return;

        // 切换到学生管理页面
        this.switchPage('students');

        // 设置搜索条件
        document.getElementById('searchInput').value = query;
        this.handleSearch();
    }
    }

    // 更新仪表板
    updateDashboard() {
        const stats = this.calculateStats();

        // 更新统计卡片
        document.getElementById('totalStudents').textContent = stats.total;
        document.getElementById('maleStudents').textContent = stats.male;
        document.getElementById('femaleStudents').textContent = stats.female;
        document.getElementById('totalClasses').textContent = stats.classes;
    }

    // 计算统计数据
    calculateStats() {
        const total = this.students.length;
        const male = this.students.filter(s => s.studentGender === '男').length;
        const female = this.students.filter(s => s.studentGender === '女').length;
        const classes = new Set(this.students.map(s => s.studentClass)).size;

        return { total, male, female, classes };
    }

    // 更新最近学生列表
    updateRecentStudents() {
        const recentList = document.getElementById('recentStudentsList');
        if (!recentList) return;

        const recentStudents = this.students
            .sort((a, b) => new Date(b.createTime || 0) - new Date(a.createTime || 0))
            .slice(0, 5);

        if (recentStudents.length === 0) {
            recentList.innerHTML = `
                <div class="empty-state">
                    <h3>暂无学生数据</h3>
                    <p>开始添加学生信息</p>
                </div>
            `;
            return;
        }

        recentList.innerHTML = recentStudents.map(student => `
            <div class="recent-item">
                <div class="recent-info">
                    <h4>${student.studentName}</h4>
                    <p>${student.studentId} - ${student.studentClass}</p>
                </div>
                <div class="recent-time">
                    ${this.formatTime(student.createTime)}
                </div>
            </div>
        `).join('');
    }

    // 更新分析页面
    updateAnalytics() {
        this.updateGenderChart();
        this.updateClassChart();
        this.updateAgeChart();
    }

    // 更新性别分布图表
    updateGenderChart() {
        const container = document.getElementById('genderChart');
        if (!container) return;

        const stats = this.calculateStats();
        container.innerHTML = `
            <div style="display: flex; justify-content: space-around; align-items: center; height: 100%;">
                <div style="text-align: center;">
                    <div style="font-size: 2rem; color: #3b82f6; font-weight: bold;">${stats.male}</div>
                    <div style="color: #6b7280;">男学生</div>
                </div>
                <div style="text-align: center;">
                    <div style="font-size: 2rem; color: #10b981; font-weight: bold;">${stats.female}</div>
                    <div style="color: #6b7280;">女学生</div>
                </div>
            </div>
        `;
    }

    // 更新班级分布图表
    updateClassChart() {
        const container = document.getElementById('classChart');
        if (!container) return;

        const classStats = {};
        this.students.forEach(student => {
            classStats[student.studentClass] = (classStats[student.studentClass] || 0) + 1;
        });

        const topClasses = Object.entries(classStats)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 5);

        if (topClasses.length === 0) {
            container.innerHTML = '<div style="text-align: center; color: #6b7280;">暂无数据</div>';
            return;
        }

        container.innerHTML = topClasses.map(([className, count]) => `
            <div style="display: flex; justify-content: space-between; margin: 10px 0; padding: 8px; background: #f3f4f6; border-radius: 4px;">
                <span style="font-weight: 500;">${className}</span>
                <span style="color: #2563eb; font-weight: bold;">${count}人</span>
            </div>
        `).join('');
    }

    // 更新年龄分布图表
    updateAgeChart() {
        const container = document.getElementById('ageChart');
        if (!container) return;

        const ageGroups = {
            '18-20': 0,
            '21-23': 0,
            '24-26': 0,
            '27+': 0
        };

        this.students.forEach(student => {
            const age = student.studentAge;
            if (age >= 18 && age <= 20) ageGroups['18-20']++;
            else if (age >= 21 && age <= 23) ageGroups['21-23']++;
            else if (age >= 24 && age <= 26) ageGroups['24-26']++;
            else if (age >= 27) ageGroups['27+']++;
        });

        container.innerHTML = Object.entries(ageGroups).map(([range, count]) => `
            <div style="display: flex; justify-content: space-between; margin: 10px 0; padding: 8px; background: #f3f4f6; border-radius: 4px;">
                <span style="font-weight: 500;">${range}岁</span>
                <span style="color: #2563eb; font-weight: bold;">${count}人</span>
            </div>
        `).join('');
    }

    // 更新系统信息
    updateSystemInfo() {
        const lastUpdateElement = document.getElementById('lastUpdate');
        if (lastUpdateElement) {
            const lastUpdate = localStorage.getItem('lastUpdate') || '从未更新';
            lastUpdateElement.textContent = lastUpdate;
        }
    }

    // 格式化时间
    formatTime(timeString) {
        if (!timeString) return '未知时间';
        const date = new Date(timeString);
        const now = new Date();
        const diff = now - date;

        if (diff < 60000) return '刚刚';
        if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`;
        if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`;
        return `${Math.floor(diff / 86400000)}天前`;
    }

    // 处理表单提交
    handleFormSubmit() {
        this.showLoading();

        setTimeout(() => {
            const formData = this.getFormData();

            // 验证数据
            if (!this.validateFormData(formData)) {
                this.hideLoading();
                return;
            }

            if (this.currentEditId) {
                // 编辑模式
                this.updateStudent(this.currentEditId, formData);
                this.showMessage('学生信息更新成功！', 'success');
            } else {
                // 添加模式
                if (this.isStudentIdExists(formData.studentId)) {
                    this.showMessage('学号已存在，请使用其他学号！', 'error');
                    this.hideLoading();
                    return;
                }
                this.addStudent(formData);
                this.showMessage('学生添加成功！', 'success');
            }

            this.hideStudentModal();
            this.renderTable();
            this.updateTotalCount();
            this.updateDashboard();
            this.updateRecentStudents();
            this.saveStudents();
            this.hideLoading();
        }, 500); // 模拟加载时间
    }

    // 获取表单数据
    getFormData() {
        return {
            studentId: document.getElementById('studentId').value.trim(),
            studentName: document.getElementById('studentName').value.trim(),
            studentAge: parseInt(document.getElementById('studentAge').value),
            studentGender: document.getElementById('studentGender').value,
            studentClass: document.getElementById('studentClass').value.trim(),
            studentPhone: document.getElementById('studentPhone').value.trim(),
            studentEmail: document.getElementById('studentEmail').value.trim()
        };
    }

    // 验证表单数据
    validateFormData(data) {
        if (!data.studentId || !data.studentName || !data.studentAge || !data.studentGender || !data.studentClass) {
            this.showMessage('请填写所有必填字段！', 'error');
            return false;
        }

        if (data.studentAge < 1 || data.studentAge > 100) {
            this.showMessage('年龄必须在1-100之间！', 'error');
            return false;
        }

        if (data.studentPhone && !/^1[3-9]\d{9}$/.test(data.studentPhone)) {
            this.showMessage('请输入正确的手机号码！', 'error');
            return false;
        }

        if (data.studentEmail && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.studentEmail)) {
            this.showMessage('请输入正确的邮箱地址！', 'error');
            return false;
        }

        return true;
    }

    // 检查学号是否存在
    isStudentIdExists(studentId) {
        return this.students.some(student => 
            student.studentId === studentId && student.id !== this.currentEditId
        );
    }

    // 添加学生
    addStudent(studentData) {
        const student = {
            id: Date.now().toString(),
            ...studentData,
            createTime: new Date().toISOString()
        };
        this.students.push(student);
        this.filteredStudents = [...this.students];

        // 更新最后修改时间
        localStorage.setItem('lastUpdate', new Date().toLocaleString());
    }

    // 更新学生
    updateStudent(id, studentData) {
        const index = this.students.findIndex(student => student.id === id);
        if (index !== -1) {
            this.students[index] = {
                ...this.students[index],
                ...studentData,
                updateTime: new Date().toLocaleString()
            };
            this.filteredStudents = [...this.students];
        }
    }

    // 删除学生
    deleteStudent(id) {
        this.students = this.students.filter(student => student.id !== id);
        this.filteredStudents = [...this.students];
        this.renderTable();
        this.updateTotalCount();
        this.saveStudents();
        this.showMessage('学生删除成功！', 'success');
    }

    // 编辑学生
    editStudent(id) {
        const student = this.students.find(s => s.id === id);
        if (student) {
            this.showStudentModal(student);
        }
    }

    // 填充表单
    fillForm(student) {
        document.getElementById('studentId').value = student.studentId;
        document.getElementById('studentName').value = student.studentName;
        document.getElementById('studentAge').value = student.studentAge;
        document.getElementById('studentGender').value = student.studentGender;
        document.getElementById('studentClass').value = student.studentClass;
        document.getElementById('studentPhone').value = student.studentPhone || '';
        document.getElementById('studentEmail').value = student.studentEmail || '';
    }

    // 显示学生模态框
    showStudentModal(student = null) {
        const modal = document.getElementById('studentModal');
        const title = document.getElementById('modalTitle');
        const submitBtn = document.getElementById('submitBtn');

        if (student) {
            // 编辑模式
            this.currentEditId = student.id;
            title.textContent = '编辑学生信息';
            submitBtn.innerHTML = '<i class="fas fa-save"></i> 更新';
            this.fillForm(student);
        } else {
            // 添加模式
            this.currentEditId = null;
            title.textContent = '添加学生';
            submitBtn.innerHTML = '<i class="fas fa-save"></i> 保存';
            this.resetForm();
        }

        modal.classList.add('show');
        modal.style.display = 'flex';
    }

    // 隐藏学生模态框
    hideStudentModal() {
        const modal = document.getElementById('studentModal');
        modal.classList.remove('show');
        setTimeout(() => {
            modal.style.display = 'none';
        }, 300);
        this.resetForm();
    }

    // 重置表单
    resetForm() {
        document.getElementById('studentForm').reset();
        this.currentEditId = null;
    }

    // 显示加载指示器
    showLoading() {
        const loading = document.getElementById('loadingIndicator');
        loading.classList.add('show');
        loading.style.display = 'flex';
    }

    // 隐藏加载指示器
    hideLoading() {
        const loading = document.getElementById('loadingIndicator');
        loading.classList.remove('show');
        setTimeout(() => {
            loading.style.display = 'none';
        }, 300);
    }

    // 全选功能
    handleSelectAll(checked) {
        const checkboxes = document.querySelectorAll('tbody input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = checked;
        });
    }

    // 处理搜索
    handleSearch() {
        const searchTerm = document.getElementById('searchInput').value.trim().toLowerCase();
        const genderFilter = document.getElementById('genderFilter').value;
        const classFilter = document.getElementById('classFilter').value.trim().toLowerCase();

        this.filteredStudents = this.students.filter(student => {
            // 文本搜索匹配
            const textMatch = !searchTerm ||
                student.studentName.toLowerCase().includes(searchTerm) ||
                student.studentId.toLowerCase().includes(searchTerm) ||
                student.studentClass.toLowerCase().includes(searchTerm);

            // 性别筛选匹配
            const genderMatch = !genderFilter || student.studentGender === genderFilter;

            // 班级筛选匹配
            const classMatch = !classFilter ||
                student.studentClass.toLowerCase().includes(classFilter);

            return textMatch && genderMatch && classMatch;
        });

        this.renderTable();
        this.updateTotalCount();
    }

    // 清除搜索
    clearSearch() {
        document.getElementById('searchInput').value = '';
        document.getElementById('genderFilter').value = '';
        document.getElementById('classFilter').value = '';
        this.filteredStudents = [...this.students];
        this.renderTable();
        this.updateTotalCount();
    }

    // 渲染表格
    renderTable() {
        const tbody = document.getElementById('studentTableBody');

        if (this.filteredStudents.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="9" class="empty-state">
                        <h3>暂无学生数据</h3>
                        <p>请添加学生信息</p>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = this.filteredStudents.map(student => `
            <tr>
                <td>
                    <input type="checkbox" value="${student.id}">
                </td>
                <td>${student.studentId}</td>
                <td>
                    <div style="font-weight: 500;">${student.studentName}</div>
                </td>
                <td>${student.studentAge}</td>
                <td>
                    <span class="gender-badge ${student.studentGender === '男' ? 'male' : 'female'}">
                        ${student.studentGender}
                    </span>
                </td>
                <td>
                    <span class="class-badge">${student.studentClass}</span>
                </td>
                <td>${student.studentPhone || '-'}</td>
                <td>${student.studentEmail || '-'}</td>
                <td>
                    <div style="display: flex; gap: 0.5rem;">
                        <button class="edit-btn" onclick="studentManager.editStudent('${student.id}')" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="delete-btn" onclick="studentManager.showDeleteModal('${student.id}')" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    // 更新总数显示
    updateTotalCount() {
        const totalElement = document.getElementById('totalCount');
        const total = this.filteredStudents.length;
        const allTotal = this.students.length;
        
        if (total === allTotal) {
            totalElement.textContent = `总计: ${total} 名学生`;
        } else {
            totalElement.textContent = `显示: ${total} 名学生 (总计: ${allTotal} 名)`;
        }
    }

    // 显示删除确认模态框
    showDeleteModal(id) {
        this.deleteTargetId = id;
        const student = this.students.find(s => s.id === id);
        if (student) {
            document.getElementById('deleteStudentName').textContent = student.studentName;
            const modal = document.getElementById('deleteModal');
            modal.classList.add('show');
            modal.style.display = 'flex';
        }
    }

    // 隐藏删除确认模态框
    hideDeleteModal() {
        const modal = document.getElementById('deleteModal');
        modal.classList.remove('show');
        setTimeout(() => {
            modal.style.display = 'none';
        }, 300);
        this.deleteTargetId = null;
    }

    // 确认删除
    confirmDelete() {
        if (this.deleteTargetId) {
            this.deleteStudent(this.deleteTargetId);
            this.hideDeleteModal();
            this.updateDashboard();
            this.updateRecentStudents();
        }
    }

    // 显示消息
    showMessage(message, type = 'info') {
        // 创建消息元素
        const messageDiv = document.createElement('div');
        messageDiv.className = `message message-${type}`;
        messageDiv.textContent = message;
        
        // 添加样式
        messageDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 5px;
            color: white;
            font-weight: bold;
            z-index: 1001;
            animation: slideIn 0.3s ease-out;
            max-width: 300px;
            word-wrap: break-word;
        `;
        
        // 根据类型设置背景色
        switch (type) {
            case 'success':
                messageDiv.style.backgroundColor = '#28a745';
                break;
            case 'error':
                messageDiv.style.backgroundColor = '#dc3545';
                break;
            default:
                messageDiv.style.backgroundColor = '#17a2b8';
        }
        
        document.body.appendChild(messageDiv);
        
        // 3秒后自动移除
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.style.animation = 'slideOut 0.3s ease-out';
                setTimeout(() => {
                    document.body.removeChild(messageDiv);
                }, 300);
            }
        }, 3000);
    }

    // 保存到本地存储
    saveStudents() {
        localStorage.setItem('students', JSON.stringify(this.students));
    }

    // 从本地存储加载
    loadStudents() {
        const saved = localStorage.getItem('students');
        return saved ? JSON.parse(saved) : [];
    }

    // 导出数据
    exportData() {
        const dataStr = JSON.stringify(this.students, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(dataBlob);

        const link = document.createElement('a');
        link.href = url;
        link.download = `学生数据_${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        this.showMessage('数据导出成功！', 'success');
    }

    // 导入数据
    importData(file) {
        if (!file) return;

        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const importedData = JSON.parse(e.target.result);

                if (!Array.isArray(importedData)) {
                    throw new Error('数据格式不正确');
                }

                // 验证数据结构
                const isValidData = importedData.every(item =>
                    item.studentId && item.studentName && item.studentAge &&
                    item.studentGender && item.studentClass
                );

                if (!isValidData) {
                    throw new Error('数据结构不完整');
                }

                // 合并数据，避免重复学号
                const existingIds = new Set(this.students.map(s => s.studentId));
                const newStudents = importedData.filter(s => !existingIds.has(s.studentId));

                this.students.push(...newStudents);
                this.filteredStudents = [...this.students];
                this.renderTable();
                this.updateTotalCount();
                this.saveStudents();

                this.showMessage(`成功导入 ${newStudents.length} 名学生数据！`, 'success');

            } catch (error) {
                this.showMessage('导入失败：' + error.message, 'error');
            }
        };

        reader.readAsText(file);
        // 清空文件输入
        document.getElementById('importFile').value = '';
    }

    // 添加示例数据
    addSampleData() {
        const sampleStudents = [
            {
                id: Date.now().toString() + '1',
                studentId: '2024001',
                studentName: '张三',
                studentAge: 20,
                studentGender: '男',
                studentClass: '计算机科学与技术1班',
                studentPhone: '13800138001',
                studentEmail: '<EMAIL>',
                createTime: new Date().toLocaleString()
            },
            {
                id: Date.now().toString() + '2',
                studentId: '2024002',
                studentName: '李四',
                studentAge: 19,
                studentGender: '女',
                studentClass: '软件工程1班',
                studentPhone: '13800138002',
                studentEmail: '<EMAIL>',
                createTime: new Date().toLocaleString()
            },
            {
                id: Date.now().toString() + '3',
                studentId: '2024003',
                studentName: '王五',
                studentAge: 21,
                studentGender: '男',
                studentClass: '计算机科学与技术1班',
                studentPhone: '13800138003',
                studentEmail: '<EMAIL>',
                createTime: new Date().toLocaleString()
            },
            {
                id: Date.now().toString() + '4',
                studentId: '2024004',
                studentName: '赵六',
                studentAge: 20,
                studentGender: '女',
                studentClass: '数据科学与大数据技术1班',
                studentPhone: '13800138004',
                studentEmail: '<EMAIL>',
                createTime: new Date().toLocaleString()
            }
        ];

        // 检查是否已存在示例数据
        const existingIds = new Set(this.students.map(s => s.studentId));
        const newSamples = sampleStudents.filter(s => !existingIds.has(s.studentId));

        if (newSamples.length === 0) {
            this.showMessage('示例数据已存在！', 'info');
            return;
        }

        this.students.push(...newSamples);
        this.filteredStudents = [...this.students];
        this.renderTable();
        this.updateTotalCount();
        this.saveStudents();

        this.showMessage(`成功添加 ${newSamples.length} 条示例数据！`, 'success');
    }

    // 清空所有数据
    clearAllData() {
        if (this.students.length === 0) {
            this.showMessage('没有数据需要清空！', 'info');
            return;
        }

        if (confirm('确定要清空所有学生数据吗？此操作不可撤销！')) {
            this.students = [];
            this.filteredStudents = [];
            this.renderTable();
            this.updateTotalCount();
            this.saveStudents();
            this.showMessage('所有数据已清空！', 'success');
        }
    }
}

// 添加CSS动画
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);

// 初始化应用
let studentManager;
document.addEventListener('DOMContentLoaded', () => {
    studentManager = new StudentManager();
});
