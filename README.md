# 学生管理系统

一个基于HTML、CSS和JavaScript开发的简单学生管理系统，支持学生信息的增删改查、搜索筛选、数据导入导出等功能。

## 功能特性

### 基本功能
- ✅ **添加学生**：支持添加学生的基本信息（学号、姓名、年龄、性别、班级、联系电话、邮箱）
- ✅ **编辑学生**：点击编辑按钮可修改学生信息
- ✅ **删除学生**：支持删除学生信息，带确认提示
- ✅ **查看学生列表**：以表格形式展示所有学生信息

### 搜索和筛选
- ✅ **文本搜索**：支持按姓名、学号、班级进行模糊搜索
- ✅ **性别筛选**：可按性别筛选学生
- ✅ **班级筛选**：可按班级进行筛选
- ✅ **组合筛选**：支持多条件组合筛选

### 数据管理
- ✅ **本地存储**：数据自动保存到浏览器本地存储
- ✅ **数据导出**：支持将学生数据导出为JSON文件
- ✅ **数据导入**：支持从JSON文件导入学生数据
- ✅ **示例数据**：一键添加示例学生数据
- ✅ **清空数据**：支持清空所有学生数据

### 用户体验
- ✅ **响应式设计**：适配不同屏幕尺寸
- ✅ **美观界面**：现代化的UI设计
- ✅ **操作提示**：操作成功/失败的消息提示
- ✅ **数据验证**：表单数据验证和错误提示
- ✅ **确认对话框**：重要操作的确认提示

## 文件结构

```
学生管理系统/
├── index.html          # 主页面文件
├── styles.css          # 样式文件
├── script.js           # JavaScript逻辑文件
└── README.md           # 说明文档
```

## 使用方法

### 1. 运行系统
直接在浏览器中打开 `index.html` 文件即可使用。

### 2. 添加学生
1. 在"添加/编辑学生信息"区域填写学生信息
2. 必填字段：学号、姓名、年龄、性别、班级
3. 可选字段：联系电话、邮箱
4. 点击"添加学生"按钮保存

### 3. 编辑学生
1. 在学生列表中找到要编辑的学生
2. 点击"编辑"按钮
3. 修改表单中的信息
4. 点击"更新学生"按钮保存修改

### 4. 删除学生
1. 在学生列表中找到要删除的学生
2. 点击"删除"按钮
3. 在确认对话框中点击"确认删除"

### 5. 搜索和筛选
- **文本搜索**：在搜索框中输入姓名、学号或班级关键词
- **性别筛选**：选择性别下拉框进行筛选
- **班级筛选**：在班级筛选框中输入班级名称
- **清除筛选**：点击"清除"按钮重置所有筛选条件

### 6. 数据管理
- **导出数据**：点击"导出数据"按钮下载JSON格式的学生数据文件
- **导入数据**：点击"导入数据"按钮选择JSON文件进行导入
- **添加示例数据**：点击"添加示例数据"按钮快速添加测试数据
- **清空数据**：点击"清空所有数据"按钮删除所有学生信息

## 数据格式

### 学生数据结构
```json
{
  "id": "唯一标识符",
  "studentId": "学号",
  "studentName": "姓名",
  "studentAge": 年龄,
  "studentGender": "性别",
  "studentClass": "班级",
  "studentPhone": "联系电话",
  "studentEmail": "邮箱",
  "createTime": "创建时间",
  "updateTime": "更新时间"
}
```

### 导入数据格式
导入的JSON文件应包含学生对象数组：
```json
[
  {
    "studentId": "2024001",
    "studentName": "张三",
    "studentAge": 20,
    "studentGender": "男",
    "studentClass": "计算机科学与技术1班",
    "studentPhone": "13800138001",
    "studentEmail": "<EMAIL>"
  }
]
```

## 技术特点

- **纯前端实现**：无需服务器，直接在浏览器中运行
- **本地存储**：使用localStorage保存数据，刷新页面数据不丢失
- **响应式设计**：支持PC和移动设备
- **模块化代码**：使用ES6类和模块化编程
- **用户友好**：丰富的交互反馈和错误处理

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 注意事项

1. 数据保存在浏览器本地存储中，清除浏览器数据会导致学生信息丢失
2. 建议定期导出数据进行备份
3. 学号必须唯一，系统会自动检查重复
4. 导入数据时会自动跳过重复学号的记录

## 开发说明

本系统采用原生HTML、CSS、JavaScript开发，无外部依赖，代码结构清晰，易于理解和扩展。

### 主要文件说明
- `index.html`：页面结构和布局
- `styles.css`：样式定义和响应式设计
- `script.js`：业务逻辑和数据处理

### 扩展建议
- 可添加更多学生字段（如专业、入学年份等）
- 可集成后端API实现数据持久化
- 可添加数据统计和图表功能
- 可实现批量操作功能
