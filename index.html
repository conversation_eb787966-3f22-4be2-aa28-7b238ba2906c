<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学生管理系统</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>学生管理系统</h1>
        </header>

        <main>
            <!-- 搜索区域 -->
            <section class="search-section">
                <div class="search-box">
                    <input type="text" id="searchInput" placeholder="搜索学生姓名、学号或班级...">
                    <select id="genderFilter">
                        <option value="">所有性别</option>
                        <option value="男">男</option>
                        <option value="女">女</option>
                    </select>
                    <input type="text" id="classFilter" placeholder="筛选班级...">
                    <button id="searchBtn">搜索</button>
                    <button id="clearSearchBtn">清除</button>
                </div>
            </section>

            <!-- 添加学生表单 -->
            <section class="form-section">
                <h2>添加/编辑学生信息</h2>
                <form id="studentForm">
                    <div class="form-group">
                        <label for="studentId">学号:</label>
                        <input type="text" id="studentId" required>
                    </div>
                    <div class="form-group">
                        <label for="studentName">姓名:</label>
                        <input type="text" id="studentName" required>
                    </div>
                    <div class="form-group">
                        <label for="studentAge">年龄:</label>
                        <input type="number" id="studentAge" min="1" max="100" required>
                    </div>
                    <div class="form-group">
                        <label for="studentGender">性别:</label>
                        <select id="studentGender" required>
                            <option value="">请选择</option>
                            <option value="男">男</option>
                            <option value="女">女</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="studentClass">班级:</label>
                        <input type="text" id="studentClass" required>
                    </div>
                    <div class="form-group">
                        <label for="studentPhone">联系电话:</label>
                        <input type="tel" id="studentPhone">
                    </div>
                    <div class="form-group">
                        <label for="studentEmail">邮箱:</label>
                        <input type="email" id="studentEmail">
                    </div>
                    <div class="form-buttons">
                        <button type="submit" id="submitBtn">添加学生</button>
                        <button type="button" id="cancelBtn">取消</button>
                    </div>
                </form>
            </section>

            <!-- 学生列表 -->
            <section class="table-section">
                <h2>学生列表</h2>
                <div class="table-container">
                    <table id="studentTable">
                        <thead>
                            <tr>
                                <th>学号</th>
                                <th>姓名</th>
                                <th>年龄</th>
                                <th>性别</th>
                                <th>班级</th>
                                <th>联系电话</th>
                                <th>邮箱</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="studentTableBody">
                            <!-- 学生数据将通过JavaScript动态添加 -->
                        </tbody>
                    </table>
                </div>
                <div class="table-info">
                    <span id="totalCount">总计: 0 名学生</span>
                    <div class="data-actions">
                        <button id="exportBtn">导出数据</button>
                        <button id="importBtn">导入数据</button>
                        <input type="file" id="importFile" accept=".json" style="display: none;">
                        <button id="addSampleBtn">添加示例数据</button>
                        <button id="clearAllBtn">清空所有数据</button>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- 确认删除模态框 -->
    <div id="deleteModal" class="modal">
        <div class="modal-content">
            <h3>确认删除</h3>
            <p>确定要删除这名学生吗？此操作不可撤销。</p>
            <div class="modal-buttons">
                <button id="confirmDeleteBtn">确认删除</button>
                <button id="cancelDeleteBtn">取消</button>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
