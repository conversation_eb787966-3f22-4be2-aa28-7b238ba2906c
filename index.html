<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学生管理系统 - 企业级管理平台</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- 侧边栏 -->
    <aside class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="logo">
                <i class="fas fa-graduation-cap"></i>
                <span>EduManager</span>
            </div>
            <button class="sidebar-toggle" id="sidebarToggle">
                <i class="fas fa-bars"></i>
            </button>
        </div>

        <nav class="sidebar-nav">
            <ul>
                <li class="nav-item active" data-page="dashboard">
                    <a href="#dashboard">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>仪表板</span>
                    </a>
                </li>
                <li class="nav-item" data-page="students">
                    <a href="#students">
                        <i class="fas fa-users"></i>
                        <span>学生管理</span>
                    </a>
                </li>
                <li class="nav-item" data-page="analytics">
                    <a href="#analytics">
                        <i class="fas fa-chart-bar"></i>
                        <span>数据分析</span>
                    </a>
                </li>
                <li class="nav-item" data-page="settings">
                    <a href="#settings">
                        <i class="fas fa-cog"></i>
                        <span>系统设置</span>
                    </a>
                </li>
            </ul>
        </nav>
    </aside>

    <!-- 主内容区域 -->
    <div class="main-wrapper">
        <!-- 顶部导航栏 -->
        <header class="topbar">
            <div class="topbar-left">
                <button class="menu-toggle" id="menuToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="breadcrumb">
                    <span id="currentPageTitle">仪表板</span>
                </div>
            </div>

            <div class="topbar-right">
                <div class="search-box-header">
                    <i class="fas fa-search"></i>
                    <input type="text" placeholder="全局搜索..." id="globalSearch">
                </div>

                <div class="user-menu">
                    <div class="notifications">
                        <i class="fas fa-bell"></i>
                        <span class="badge">3</span>
                    </div>
                    <div class="user-profile">
                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiM2NjdlZWEiLz4KPHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSI+CjxwYXRoIGQ9Ik0yMCAyMEM5IDIwIDkgMTUgMTUgMTVIMjVDMzEgMTUgMzEgMjAgMjAgMjBaIiBmaWxsPSJ3aGl0ZSIvPgo8Y2lyY2xlIGN4PSIyMCIgY3k9IjEyIiByPSI1IiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4KPC9zdmc+" alt="用户头像" class="avatar">
                        <span class="username">管理员</span>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                </div>
            </div>
        </header>

        <!-- 主内容 -->
        <main class="main-content">
            <!-- 仪表板页面 -->
            <div class="page-content active" id="dashboard-page">
                <div class="page-header">
                    <h1>仪表板概览</h1>
                    <p>学生管理系统数据概览</p>
                </div>

                <!-- 统计卡片 -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="totalStudents">0</h3>
                            <p>总学生数</p>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-user-graduate"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="maleStudents">0</h3>
                            <p>男学生</p>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-female"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="femaleStudents">0</h3>
                            <p>女学生</p>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-school"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="totalClasses">0</h3>
                            <p>班级数量</p>
                        </div>
                    </div>
                </div>

                <!-- 快速操作 -->
                <div class="quick-actions">
                    <h2>快速操作</h2>
                    <div class="action-cards">
                        <div class="action-card" onclick="switchPage('students')">
                            <i class="fas fa-user-plus"></i>
                            <h3>添加学生</h3>
                            <p>快速添加新学生信息</p>
                        </div>
                        <div class="action-card" onclick="studentManager.exportData()">
                            <i class="fas fa-download"></i>
                            <h3>导出数据</h3>
                            <p>导出学生数据备份</p>
                        </div>
                        <div class="action-card" onclick="document.getElementById('importFile').click()">
                            <i class="fas fa-upload"></i>
                            <h3>导入数据</h3>
                            <p>从文件导入学生数据</p>
                        </div>
                        <div class="action-card" onclick="switchPage('analytics')">
                            <i class="fas fa-chart-line"></i>
                            <h3>数据分析</h3>
                            <p>查看详细统计分析</p>
                        </div>
                    </div>
                </div>

                <!-- 最近添加的学生 -->
                <div class="recent-students">
                    <h2>最近添加的学生</h2>
                    <div class="recent-list" id="recentStudentsList">
                        <!-- 动态生成 -->
                    </div>
                </div>
            </div>

            <!-- 学生管理页面 -->
            <div class="page-content" id="students-page">
                <div class="page-header">
                    <h1>学生管理</h1>
                    <div class="page-actions">
                        <button class="btn btn-primary" id="addStudentBtn">
                            <i class="fas fa-plus"></i>
                            添加学生
                        </button>
                        <button class="btn btn-secondary" id="exportBtn">
                            <i class="fas fa-download"></i>
                            导出数据
                        </button>
                    </div>
                </div>

                <!-- 搜索和筛选 -->
                <div class="filters-section">
                    <div class="search-filters">
                        <div class="search-input-group">
                            <i class="fas fa-search"></i>
                            <input type="text" id="searchInput" placeholder="搜索学生姓名、学号或班级...">
                        </div>
                        <select id="genderFilter" class="filter-select">
                            <option value="">所有性别</option>
                            <option value="男">男</option>
                            <option value="女">女</option>
                        </select>
                        <input type="text" id="classFilter" placeholder="筛选班级..." class="filter-input">
                        <button class="btn btn-outline" id="clearSearchBtn">
                            <i class="fas fa-times"></i>
                            清除筛选
                        </button>
                    </div>
                </div>

                <!-- 学生列表 -->
                <div class="data-table-container">
                    <div class="table-header">
                        <h3>学生列表</h3>
                        <div class="table-actions">
                            <span id="totalCount">总计: 0 名学生</span>
                            <button class="btn btn-sm" id="addSampleBtn">添加示例数据</button>
                        </div>
                    </div>

                    <div class="table-wrapper">
                        <table class="data-table" id="studentTable">
                            <thead>
                                <tr>
                                    <th>
                                        <input type="checkbox" id="selectAll">
                                    </th>
                                    <th>学号</th>
                                    <th>姓名</th>
                                    <th>年龄</th>
                                    <th>性别</th>
                                    <th>班级</th>
                                    <th>联系电话</th>
                                    <th>邮箱</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="studentTableBody">
                                <!-- 学生数据将通过JavaScript动态添加 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 数据分析页面 -->
            <div class="page-content" id="analytics-page">
                <div class="page-header">
                    <h1>数据分析</h1>
                    <p>学生数据统计与分析</p>
                </div>

                <div class="analytics-grid">
                    <div class="chart-card">
                        <h3>性别分布</h3>
                        <div class="chart-container" id="genderChart">
                            <!-- 图表将通过JavaScript生成 -->
                        </div>
                    </div>

                    <div class="chart-card">
                        <h3>班级分布</h3>
                        <div class="chart-container" id="classChart">
                            <!-- 图表将通过JavaScript生成 -->
                        </div>
                    </div>

                    <div class="chart-card">
                        <h3>年龄分布</h3>
                        <div class="chart-container" id="ageChart">
                            <!-- 图表将通过JavaScript生成 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 系统设置页面 -->
            <div class="page-content" id="settings-page">
                <div class="page-header">
                    <h1>系统设置</h1>
                    <p>管理系统配置和数据</p>
                </div>

                <div class="settings-grid">
                    <div class="setting-card">
                        <h3>数据管理</h3>
                        <div class="setting-actions">
                            <button class="btn btn-primary" id="importBtn">
                                <i class="fas fa-upload"></i>
                                导入数据
                            </button>
                            <button class="btn btn-danger" id="clearAllBtn">
                                <i class="fas fa-trash"></i>
                                清空所有数据
                            </button>
                        </div>
                    </div>

                    <div class="setting-card">
                        <h3>系统信息</h3>
                        <div class="system-info">
                            <p><strong>版本:</strong> 1.0.0</p>
                            <p><strong>最后更新:</strong> <span id="lastUpdate">-</span></p>
                            <p><strong>数据存储:</strong> 本地浏览器存储</p>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 学生表单模态框 -->
    <div id="studentModal" class="modal">
        <div class="modal-content large">
            <div class="modal-header">
                <h3 id="modalTitle">添加学生</h3>
                <button class="modal-close" id="closeModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="modal-body">
                <form id="studentForm">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="studentId">学号 <span class="required">*</span></label>
                            <input type="text" id="studentId" required>
                        </div>

                        <div class="form-group">
                            <label for="studentName">姓名 <span class="required">*</span></label>
                            <input type="text" id="studentName" required>
                        </div>

                        <div class="form-group">
                            <label for="studentAge">年龄 <span class="required">*</span></label>
                            <input type="number" id="studentAge" min="1" max="100" required>
                        </div>

                        <div class="form-group">
                            <label for="studentGender">性别 <span class="required">*</span></label>
                            <select id="studentGender" required>
                                <option value="">请选择</option>
                                <option value="男">男</option>
                                <option value="女">女</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="studentClass">班级 <span class="required">*</span></label>
                            <input type="text" id="studentClass" required>
                        </div>

                        <div class="form-group">
                            <label for="studentPhone">联系电话</label>
                            <input type="tel" id="studentPhone">
                        </div>

                        <div class="form-group full-width">
                            <label for="studentEmail">邮箱</label>
                            <input type="email" id="studentEmail">
                        </div>
                    </div>
                </form>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="cancelBtn">取消</button>
                <button type="submit" class="btn btn-primary" id="submitBtn" form="studentForm">
                    <i class="fas fa-save"></i>
                    保存
                </button>
            </div>
        </div>
    </div>

    <!-- 确认删除模态框 -->
    <div id="deleteModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>确认删除</h3>
                <button class="modal-close" id="closeDeleteModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="modal-body">
                <div class="delete-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <p>确定要删除这名学生吗？此操作不可撤销。</p>
                </div>
            </div>

            <div class="modal-footer">
                <button class="btn btn-secondary" id="cancelDeleteBtn">取消</button>
                <button class="btn btn-danger" id="confirmDeleteBtn">
                    <i class="fas fa-trash"></i>
                    确认删除
                </button>
            </div>
        </div>
    </div>

    <!-- 隐藏的文件输入 -->
    <input type="file" id="importFile" accept=".json" style="display: none;">

    <!-- 加载指示器 -->
    <div id="loadingIndicator" class="loading-indicator">
        <div class="spinner"></div>
        <p>处理中...</p>
    </div>

    <script src="script.js"></script>
</body>
</html>
